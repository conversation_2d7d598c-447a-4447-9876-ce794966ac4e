import { handleTaobaoApiResponse } from './utils/response-handler.js';
import { database } from './database.js';
import { CaptchaSolver } from './api/captcha-solver.js';
import { updateAnchorCookie } from './utils/cookie-utils.js';
import crypto from 'crypto';

/**
 * 直播订单同步工具
 */
class LiveOrderSync {
    constructor() {
        this.appKey = '12574478';
        this.apiVersion = '1.0';
        this.baseUrl = 'https://h5api.m.taobao.com/h5/mtop.dreamweb.query.general.generalquery/1.0/';
        this.pageSize = 1000; // 每页获取1000条数据，因为如果每页获取的数量少，不同页之间会有重复订单，导致数据不完整
        this.captchaSolver = new CaptchaSolver(); // 初始化滑块解密器
    }

    /**
     * 构建请求URL和参数 (JSON格式)
     * @param {string} h5Token
     * @param {object} data
     * @returns {object}
     */
    buildRequestUrl(h5Token, data) {
        const timestamp = Date.now();

        // 构建参数字符串（用于签名计算）
        const paramStr = JSON.stringify({
            dataApi: "dataQRForm",
            param: JSON.stringify({
                dataQRFormId: "live_overview_order",
                queryUserRole: "ALL",
                beginTime: data.beginTime,
                endTime: data.endTime,
                orderDateType: "3",
                start: data.start.toString(), // 页数从0开始
                hit: data.hit.toString() // 每页数据量
            })
        });

        // 这里需要正确的签名算法，暂时使用示例中的签名
        const sign = this.generateSign(h5Token, timestamp, paramStr);

        const params = new URLSearchParams({
            jsv: '2.7.4',
            appKey: this.appKey,
            t: timestamp.toString(),
            sign: sign,
            api: 'mtop.dreamweb.query.general.generalQuery',
            v: this.apiVersion,
            dataType: 'json',
            preventFallback: 'true',
            type: 'json',
            data: paramStr
        });

        return {
            url: `${this.baseUrl}?${params.toString()}`,
            timestamp,
            sign
        };
    }

    /**
     * MD5哈希函数
     * @param {string} string 
     * @returns {string}
     */
    md5(string) {
        return crypto.createHash('md5').update(string).digest('hex');
    }

    /**
     * 生成签名
     * @param {string} h5Token
     * @param {number} timestamp
     * @param {string} data
     * @returns {string}
     */
    generateSign(h5Token, timestamp, data) {
        const signString = `${h5Token}&${timestamp}&${this.appKey}&${data}`;
        return this.md5(signString);
    }

    /**
     * 获取订单总数
     * @param {string} h5Token
     * @param {string} fullCookie
     * @param {string} anchorName
     * @param {string} beginTime
     * @param {string} endTime
     * @returns {Promise<object>}
     */
    async fetchOrderCount(h5Token, fullCookie, anchorName, beginTime, endTime) {
        try {
            return await this._fetchOrderCountWithRetry(h5Token, fullCookie, anchorName, beginTime, endTime, 0);
        } catch (error) {
            console.error('获取订单总数失败:', error);
            return {
                success: false,
                error: error.message,
                totalCount: 0
            };
        }
    }

    /**
     * 获取订单总数的内部实现，支持滑块解密重试
     * @param {string} h5Token
     * @param {string} fullCookie
     * @param {string} anchorName
     * @param {string} beginTime
     * @param {string} endTime
     * @param {number} retryCount - 重试次数
     * @returns {Promise<object>}
     */
    async _fetchOrderCountWithRetry(h5Token, fullCookie, anchorName, beginTime, endTime, retryCount) {
        const maxRetries = 1; // 最多重试1次

        try {
            const timestamp = Date.now();

            // 构建获取订单总数的参数
            const paramStr = JSON.stringify({
                dataApi: "live_trd_pay_ord_filter_stat",
                param: JSON.stringify({
                    beginTime,
                    endTime,
                    orderDateType: "3"
                })
            });

            const sign = this.generateSign(h5Token, timestamp, paramStr);

            const params = new URLSearchParams({
                jsv: '2.7.4',
                appKey: this.appKey,
                t: timestamp.toString(),
                sign: sign,
                api: 'mtop.dreamweb.query.general.generalQuery',
                v: this.apiVersion,
                dataType: 'json', // 改为json格式
                preventFallback: 'true',
                type: 'json', // 改为json格式
                data: paramStr
            });

            const url = `${this.baseUrl}?${params.toString()}`;

            const response = await fetch(url, {
                method: 'GET',
                headers: {
                    'Accept': '*/*',
                    'Accept-Language': 'zh-CN,zh;q=0.9',
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                    'Referer': 'https://liveplatform.taobao.com/restful/index/data/transaction',
                    'Cookie': fullCookie,
                    'Sec-Ch-Ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"',
                    'Sec-Ch-Ua-Mobile': '?0',
                    'Sec-Ch-Ua-Platform': '"Windows"',
                    'Sec-Fetch-Dest': 'script',
                    'Sec-Fetch-Mode': 'no-cors',
                    'Sec-Fetch-Site': 'same-site'
                }
            });

            // 使用统一的响应处理
            const responseResult = await handleTaobaoApiResponse(response, anchorName, fullCookie);
            if (!responseResult.success) {
                // 检查是否是滑块验证错误
                if (responseResult.error && responseResult.error.includes('FAIL_SYS_USER_VALIDATE') && retryCount < maxRetries) {
                    console.log(`🔓 检测到滑块验证错误，开始解密流程... (重试次数: ${retryCount + 1}/${maxRetries + 1})`);

                    try {
                        // 构造API响应对象供滑块解密器使用
                        const apiResponse = {
                            error: responseResult.error,
                            response: response
                        };

                        // 调用滑块解密
                        const updatedCookie = await this.captchaSolver.solveCaptcha(apiResponse, url, fullCookie);

                        // 更新主播的cookie到数据库
                        const cookieMap = {};
                        updatedCookie.split(';').forEach(cookie => {
                            const [name, value] = cookie.trim().split('=');
                            if (name && value) {
                                cookieMap[name] = value;
                            }
                        });

                        await updateAnchorCookie(anchorName, cookieMap, fullCookie);
                        console.log(`✅ 主播 ${anchorName} 的cookie已更新，准备重试请求`);

                        // 使用更新后的cookie重试请求
                        return await this._fetchOrderCountWithRetry(h5Token, updatedCookie, anchorName, beginTime, endTime, retryCount + 1);

                    } catch (captchaError) {
                        console.error('❌ 滑块解密失败:', captchaError);
                        return {
                            success: false,
                            error: `滑块解密失败: ${captchaError.message}`,
                            errorType: 'CAPTCHA_SOLVE_FAILED',
                            cookieUpdated: responseResult.cookieUpdated,
                            totalCount: 0
                        };
                    }
                } else {
                    return {
                        success: false,
                        error: responseResult.error,
                        errorType: responseResult.errorType,
                        cookieUpdated: responseResult.cookieUpdated,
                        totalCount: 0
                    };
                }
            }

            // 解析订单总数
            let totalCount = 0;
            if (responseResult.data && responseResult.data.result && responseResult.data.result.length > 0) {
                const result = responseResult.data.result[0];
                totalCount = parseInt(result.pay_ord_cnt || '0');
            }

            console.log(`订单总数: ${totalCount}`);

            return {
                success: true,
                totalCount,
                cookieUpdated: responseResult.cookieUpdated || (retryCount > 0 ? fullCookie : null)
            };

        } catch (error) {
            console.error('获取订单总数失败:', error);
            throw error;
        }
    }

    /**
     * 获取单页订单数据 (JSON格式)
     * @param {string} h5Token
     * @param {string} fullCookie
     * @param {string} anchorName
     * @param {string} beginTime
     * @param {string} endTime
     * @param {number} start - 偏移量，第一页=0，第二页=10，第三页=20...
     * @returns {Promise<object>}
     */
    async fetchOrderPage(h5Token, fullCookie, anchorName, beginTime, endTime, start = 0) {
        try {
            return await this._fetchOrderPageWithRetry(h5Token, fullCookie, anchorName, beginTime, endTime, start, 0);
        } catch (error) {
            console.error('获取订单数据失败:', error);
            return {
                success: false,
                error: error.message,
                data: []
            };
        }
    }

    /**
     * 获取单页订单数据的内部实现，支持滑块解密重试
     * @param {string} h5Token
     * @param {string} fullCookie
     * @param {string} anchorName
     * @param {string} beginTime
     * @param {string} endTime
     * @param {number} start
     * @param {number} retryCount - 重试次数
     * @returns {Promise<object>}
     */
    async _fetchOrderPageWithRetry(h5Token, fullCookie, anchorName, beginTime, endTime, start, retryCount) {
        const maxRetries = 1; // 最多重试1次

        try {
            const data = {
                beginTime,
                endTime,
                start,
                hit: this.pageSize
            };

            const { url } = this.buildRequestUrl(h5Token, data);

            // 打印请求的data参数
            console.log(`请求参数 data:`, JSON.stringify(data, null, 2));

            const response = await fetch(url, {
                method: 'GET',
                headers: {
                    'accept': '*/*',
                    'accept-language': 'zh-CN,zh;q=0.9',
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                    'Referer': 'https://liveplatform.taobao.com/restful/index/data/transaction',
                    'Cookie': fullCookie,
                    'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"',
                    'sec-ch-ua-mobile': '?0',
                    'sec-ch-ua-platform': '"Windows"',
                    'sec-fetch-dest': 'script',
                    'sec-fetch-mode': 'no-cors',
                    'sec-fetch-site': 'same-site'
                },
                mode: 'cors',
                credentials: 'include'
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            // 获取JSON响应
            const responseData = await response.json();

            // 检查API响应状态
            if (!responseData.ret || !responseData.ret.some(ret => ret.includes('SUCCESS'))) {
                const errorMsg = responseData.ret ? responseData.ret.join(', ') : '未知错误';
                console.error('API返回错误:', responseData.ret);

                // 检查是否是滑块验证错误
                if (errorMsg.includes('FAIL_SYS_USER_VALIDATE') && retryCount < maxRetries) {
                    console.log(`🔓 检测到滑块验证错误，开始解密流程... (重试次数: ${retryCount + 1}/${maxRetries + 1})`);

                    try {
                        // 构造API响应对象供滑块解密器使用
                        const apiResponse = {
                            error: errorMsg,
                            response: response
                        };

                        // 调用滑块解密
                        const updatedCookie = await this.captchaSolver.solveCaptcha(apiResponse, url, fullCookie);

                        // 更新主播的cookie到数据库
                        const cookieMap = {};
                        updatedCookie.split(';').forEach(cookie => {
                            const [name, value] = cookie.trim().split('=');
                            if (name && value) {
                                cookieMap[name] = value;
                            }
                        });

                        await updateAnchorCookie(anchorName, cookieMap, fullCookie);
                        console.log(`✅ 主播 ${anchorName} 的cookie已更新，准备重试请求`);

                        // 使用更新后的cookie重试请求
                        return await this._fetchOrderPageWithRetry(h5Token, updatedCookie, anchorName, beginTime, endTime, start, retryCount + 1);

                    } catch (captchaError) {
                        console.error('❌ 滑块解密失败:', captchaError);
                        return {
                            success: false,
                            error: `滑块解密失败: ${captchaError.message}`,
                            errorType: 'CAPTCHA_SOLVE_FAILED',
                            data: []
                        };
                    }
                } else {
                    return {
                        success: false,
                        error: `API错误: ${errorMsg}`,
                        errorType: errorMsg.includes('FAIL_SYS_USER_VALIDATE') ? 'CAPTCHA_REQUIRED' : 'API_ERROR',
                        data: []
                    };
                }
            }

            // 解析订单数据
            let orders = [];
            if (responseData.data && responseData.data.result) {
                orders = responseData.data.result;
            }

            const pageNumber = Math.floor(start / this.pageSize) + 1;
            console.log(`第 ${pageNumber} 页获取到 ${orders.length} 条订单数据`);

            return {
                success: true,
                data: orders,
                cookieUpdated: retryCount > 0 ? fullCookie : null // 如果进行了重试，说明cookie已更新
            };

        } catch (error) {
            console.error('获取订单数据失败:', error);
            throw error;
        }
    }

    /**
     * 获取所有订单数据（分页）
     * @param {string} h5Token
     * @param {string} fullCookie
     * @param {string} anchorName
     * @param {string} beginTime
     * @param {string} endTime
     * @returns {Promise<object>}
     */
    async fetchOrders(h5Token, fullCookie, anchorName, beginTime, endTime) {
        try {
            // 首先获取订单总数
            console.log('正在获取订单总数...');
            const countResult = await this.fetchOrderCount(h5Token, fullCookie, anchorName, beginTime, endTime);
            
            if (!countResult.success) {
                return {
                    success: false,
                    error: countResult.error,
                    errorType: countResult.errorType,
                    cookieUpdated: countResult.cookieUpdated,
                    data: []
                };
            }

            const totalCount = countResult.totalCount;
            if (totalCount === 0) {
                console.log('没有订单数据');
                return {
                    success: true,
                    data: [],
                    cookieUpdated: countResult.cookieUpdated
                };
            }

            // 计算需要获取的页数
            const totalPages = Math.ceil(totalCount / this.pageSize);
            console.log(`订单总数: ${totalCount}，需要获取 ${totalPages} 页数据`);

            const allOrders = [];
            let lastCookieUpdate = countResult.cookieUpdated;

            // 按页数获取数据
            for (let pageIndex = 0; pageIndex < totalPages; pageIndex++) {
                const startOffset = pageIndex * this.pageSize; // 计算偏移量
                console.log(`正在获取第 ${pageIndex + 1}/${totalPages} 页订单数据 (start=${startOffset})...`);
                
                const result = await this.fetchOrderPage(h5Token, fullCookie, anchorName, beginTime, endTime, startOffset);
                
                if (!result.success) {
                    console.error(`获取第 ${pageIndex + 1} 页数据失败:`, result.error);
                    // 如果某页失败，返回已获取的数据
                    return {
                        success: false,
                        error: result.error,
                        errorType: result.errorType,
                        cookieUpdated: result.cookieUpdated || lastCookieUpdate,
                        data: allOrders
                    };
                }

                // 记录cookie更新
                if (result.cookieUpdated) {
                    lastCookieUpdate = result.cookieUpdated;
                }

                // 添加获取到的数据
                if (result.data && result.data.length > 0) {
                    allOrders.push(...result.data);
                    console.log(`第 ${pageIndex + 1} 页获取到 ${result.data.length} 条数据，累计 ${allOrders.length} 条`);
                } else {
                    console.log(`第 ${pageIndex + 1} 页没有数据，提前结束`);
                    break;
                }

                // 添加延迟避免请求过于频繁
                if (pageIndex < totalPages - 1) {
                    await new Promise(resolve => setTimeout(resolve, 500));
                }
            }

            console.log(`订单数据获取完成，总共获取到 ${allOrders.length} 条订单数据`);

            // 检测重复的订单ID
            const duplicates = this.detectDuplicateOrders(allOrders);

            // 去重处理
            const uniqueOrders = this.removeDuplicateOrders(allOrders);
            console.log(`去重后剩余 ${uniqueOrders.length} 条唯一订单数据`);

            return {
                success: true,
                data: uniqueOrders,
                cookieUpdated: lastCookieUpdate,
                duplicatesFound: duplicates.length,
                originalCount: allOrders.length,
                uniqueCount: uniqueOrders.length
            };

        } catch (error) {
            console.error('获取订单数据失败:', error);
            return {
                success: false,
                error: error.message,
                data: []
            };
        }
    }

    /**
     * 检测重复的订单ID
     * @param {Array} orders - 订单数组
     */
    detectDuplicateOrders(orders) {
        const orderIdMap = new Map();
        const duplicates = [];

        orders.forEach((order, index) => {
            const orderId = order.orderId;
            if (orderIdMap.has(orderId)) {
                // 发现重复
                const firstIndex = orderIdMap.get(orderId);
                duplicates.push({
                    orderId,
                    firstIndex,
                    duplicateIndex: index,
                    firstOrder: orders[firstIndex],
                    duplicateOrder: order
                });
            } else {
                orderIdMap.set(orderId, index);
            }
        });

        if (duplicates.length > 0) {
            console.log(`发现 ${duplicates.length} 个重复的订单ID`);
        }

        return duplicates;
    }

    /**
     * 去除重复的订单数据
     * @param {Array} orders - 订单数组
     * @returns {Array} - 去重后的订单数组
     */
    removeDuplicateOrders(orders) {
        const uniqueOrdersMap = new Map();
        const uniqueOrders = [];

        orders.forEach((order) => {
            const orderId = order.orderId;
            if (!uniqueOrdersMap.has(orderId)) {
                uniqueOrdersMap.set(orderId, true);
                uniqueOrders.push(order);
            }
        });

        console.log(`去重处理: 原始${orders.length}条 → 去重后${uniqueOrders.length}条`);

        return uniqueOrders;
    }

    /**
     * 转换订单数据格式
     * @param {Array} rawOrders
     * @param {string} anchorName
     * @returns {Array}
     */
    transformOrders(rawOrders, anchorName) {
        return rawOrders.map(order => {
            return {
                anchor_name: anchorName,
                live_id: order.contentId || '',
                item_title: order.itemTitle || '',
                item_id: order.itemId || '',
                pay_time: order.payTime || null,
                pay_amount: parseFloat(order.divPayAmt || '0'),
                refund_amount: order.refundAmt && order.refundAmt !== '-' ? parseFloat(order.refundAmt) : 0,
                quantity: 1, // 订单数据中没有数量字段，默认为1
                parent_order_id: order.mordId || '',
                child_order_id: order.orderId || ''
            };
        });
    }

    /**
     * 保存订单数据到数据库
     * @param {Array} orders
     * @returns {Promise<object>}
     */
    async saveOrders(orders) {
        if (!orders || orders.length === 0) {
            return {
                success: true,
                inserted: 0,
                updated: 0,
                message: '没有订单数据需要保存'
            };
        }

        console.log(`开始保存 ${orders.length} 条订单数据...`);
        

        const db = database;

        let inserted = 0;
        let updated = 0;
        const errors = [];

        try {
            // 使用数据库的事务方法
            const result = await db.transaction(async () => {
                for (const order of orders) {
                    try {
                        // 先查询是否存在该订单
                        const existingOrder = await db.get(`
                            SELECT id, created_at FROM live_orders 
                            WHERE child_order_id = ?
                        `, [order.child_order_id]);

                        if (existingOrder) {
                            // 订单已存在，更新数据
                            const updateResult = await db.run(`
                                UPDATE live_orders SET
                                    anchor_name = ?,
                                    live_id = ?,
                                    item_title = ?,
                                    item_id = ?,
                                    pay_time = ?,
                                    pay_amount = ?,
                                    refund_amount = ?,
                                    quantity = ?,
                                    parent_order_id = ?,
                                    updated_at = datetime('now', '+8 hours')
                                WHERE child_order_id = ?
                            `, [
                                order.anchor_name,
                                order.live_id,
                                order.item_title,
                                order.item_id,
                                order.pay_time,
                                order.pay_amount,
                                order.refund_amount,
                                order.quantity,
                                order.parent_order_id,
                                order.child_order_id
                            ]);

                            if (updateResult.meta.changes > 0) {
                                updated++;
                            }
                        } else {
                            // 订单不存在，插入新数据
                            const insertResult = await db.run(`
                                INSERT INTO live_orders (
                                    anchor_name, live_id, item_title, item_id,
                                    pay_time, pay_amount, refund_amount, quantity,
                                    parent_order_id, child_order_id,
                                    created_at, updated_at
                                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 
                                    datetime('now', '+8 hours'), datetime('now', '+8 hours'))
                            `, [
                                order.anchor_name,
                                order.live_id,
                                order.item_title,
                                order.item_id,
                                order.pay_time,
                                order.pay_amount,
                                order.refund_amount,
                                order.quantity,
                                order.parent_order_id,
                                order.child_order_id
                            ]);

                            if (insertResult.meta.changes > 0) {
                                inserted++;
                            }
                        }
                        
                    } catch (error) {
                        console.error(`保存订单数据失败 ${order.child_order_id}:`, error);
                        errors.push(`订单 ${order.child_order_id}: ${error.message}`);
                    }
                }
                
                return { inserted, updated };
            });

            console.log(`订单保存完成: 新增 ${result.inserted} 条，更新 ${result.updated} 条，总计处理 ${orders.length} 条`);

            return {
                success: true,
                inserted: result.inserted,
                updated: result.updated,
                total: orders.length,
                errors: errors.length > 0 ? errors : null
            };

        } catch (error) {
            console.error('保存订单数据失败:', error);
            return {
                success: false,
                error: error.message,
                inserted: 0,
                updated: 0
            };
        }
    }

    /**
     * 同步指定主播的订单数据
     * @param {string} anchorName
     * @param {string} h5Token
     * @param {string} fullCookie
     * @param {string} beginTime
     * @param {string} endTime
     * @returns {Promise<object>}
     */
    async syncAnchorOrders(anchorName, h5Token, fullCookie, beginTime, endTime) {
        try {
            console.log(`开始同步主播 ${anchorName} 的订单数据，时间范围: ${beginTime} - ${endTime}`);

            // 获取订单数据（已包含去重处理）
            const result = await this.fetchOrders(h5Token, fullCookie, anchorName, beginTime, endTime);

            if (!result.success) {
                return {
                    success: false,
                    error: result.error,
                    anchorName,
                    inserted: 0,
                    updated: 0,
                    total: 0,
                    duplicatesFound: 0,
                    originalCount: 0,
                    uniqueCount: 0
                };
            }

            // 转换数据格式（result.data已经是去重后的数据）
            const orders = this.transformOrders(result.data, anchorName);

            // 保存到数据库
            const saveResult = await this.saveOrders(orders);

            // 如果订单保存成功，更新直播计划的销售金额统计
            if (saveResult.success) {
                console.log(`订单同步完成，开始更新主播 ${anchorName} 的直播计划销售金额统计...`);
                const updateResult = await this.updateLivePlanSalesStats(anchorName);
                if (updateResult.success) {
                    console.log(`✅ 主播 ${anchorName} 的直播计划销售金额统计更新完成，更新了 ${updateResult.updatedCount} 个直播计划`);
                } else {
                    console.error(`❌ 更新主播 ${anchorName} 的直播计划销售金额统计失败:`, updateResult.error);
                }
            }

            return {
                success: saveResult.success,
                error: saveResult.error,
                anchorName,
                inserted: saveResult.inserted,
                updated: saveResult.updated,
                total: orders.length,
                cookieUpdated: result.cookieUpdated,
                duplicatesFound: result.duplicatesFound || 0,
                originalCount: result.originalCount || orders.length,
                uniqueCount: result.uniqueCount || orders.length
            };

        } catch (error) {
            console.error('同步订单数据失败:', error);
            return {
                success: false,
                error: error.message,
                anchorName,
                inserted: 0,
                updated: 0,
                total: 0,
                duplicatesFound: 0,
                originalCount: 0,
                uniqueCount: 0
            };
        }
    }

    /**
     * 更新指定主播的直播计划销售金额统计
     * @param {string} anchorName
     * @returns {Promise<object>}
     */
    async updateLivePlanSalesStats(anchorName) {
        try {
            console.log(`开始统计主播 ${anchorName} 的订单销售金额...`);

            // 查询该主播所有订单，按live_id分组统计销售金额
            const salesStatsResult = await database.all(`
                SELECT
                    live_id,
                    COUNT(*) as order_count,
                    COALESCE(SUM(pay_amount - refund_amount), 0) as total_sales,
                    COALESCE(SUM(pay_amount), 0) as gross_sales,
                    COALESCE(SUM(refund_amount), 0) as total_refunds
                FROM live_orders
                WHERE anchor_name = ?
                GROUP BY live_id
            `, [anchorName]);

            if (!salesStatsResult || !salesStatsResult.results || salesStatsResult.results.length === 0) {
                console.log(`主播 ${anchorName} 暂无订单数据`);
                return {
                    success: true,
                    updatedCount: 0,
                    message: '暂无订单数据需要统计'
                };
            }

            const salesStats = salesStatsResult.results;
            console.log(`查询到主播 ${anchorName} 的 ${salesStats.length} 个直播的订单统计数据`);

            let updatedCount = 0;
            const errors = [];

            // 使用事务批量更新直播计划表
            const transactionResult = await database.transaction(async () => {
                let localUpdatedCount = 0;

                for (const stat of salesStats) {
                    try {
                        // 检查该live_id是否存在于直播计划表中
                        const livePlan = await database.get(`
                            SELECT id, live_id, total_sales
                            FROM live_plans
                            WHERE live_id = ? AND anchor_name = ?
                        `, [stat.live_id, anchorName]);

                        if (livePlan) {
                            // 更新直播计划表的销售金额
                            const updateResult = await database.run(`
                                UPDATE live_plans SET
                                    total_sales = ?,
                                    updated_at = datetime('now', '+8 hours')
                                WHERE live_id = ? AND anchor_name = ?
                            `, [
                                parseFloat(stat.total_sales) || 0,
                                stat.live_id,
                                anchorName
                            ]);

                            if (updateResult.meta.changes > 0) {
                                localUpdatedCount++;
                                console.log(`✅ 更新直播计划 ${stat.live_id} 销售金额: ${stat.total_sales} (订单数: ${stat.order_count})`);
                            }
                        } else {
                            console.log(`⚠️ 直播计划表中未找到 live_id: ${stat.live_id}，跳过更新`);
                        }
                    } catch (error) {
                        console.error(`更新直播计划 ${stat.live_id} 销售金额失败:`, error);
                        errors.push(`live_id ${stat.live_id}: ${error.message}`);
                        // 不抛出错误，继续处理其他记录
                    }
                }

                return localUpdatedCount;
            });

            updatedCount = transactionResult;

            console.log(`主播 ${anchorName} 直播计划销售金额统计更新完成，成功更新 ${updatedCount} 个直播计划`);

            return {
                success: true,
                updatedCount,
                totalStats: salesStats.length,
                errors: errors.length > 0 ? errors : undefined
            };

        } catch (error) {
            console.error(`更新主播 ${anchorName} 直播计划销售金额统计失败:`, error);
            return {
                success: false,
                error: error.message,
                updatedCount: 0
            };
        }
    }
}

export default LiveOrderSync;