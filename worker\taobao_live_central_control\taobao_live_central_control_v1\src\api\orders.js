import { database } from '../database.js';
import LiveOrderSync from '../live-order-sync.js';

// 创建订单同步实例
const orderSync = new LiveOrderSync();

/**
 * 构建WHERE条件和参数（提取公共逻辑）
 * @param {Object} filters - 筛选条件
 * @returns {Object} - {whereClause, params}
 */
function buildWhereConditions(filters) {
    const {
        anchor,
        timeRange,
        startDate,
        endDate,
        itemSearch,
        liveId,
        minAmount,
        maxAmount
    } = filters;

    let whereConditions = [];
    let params = [];

    // 主播筛选
    if (anchor && anchor.trim() !== '') {
        whereConditions.push('anchor_name = ?');
        params.push(anchor.trim());
    }

    // 时间范围筛选
    if (timeRange && timeRange !== '') {
        const days = parseInt(timeRange);
        if (!isNaN(days) && days > 0) {
            whereConditions.push("pay_time >= date('now', '-' || ? || ' days')");
            params.push(days);
        }
    }

    // 自定义日期范围
    if (startDate && startDate.trim() !== '') {
        whereConditions.push('pay_time >= ?');
        params.push(startDate.trim() + ' 00:00:00');
    }

    if (endDate && endDate.trim() !== '') {
        whereConditions.push('pay_time <= ?');
        params.push(endDate.trim() + ' 23:59:59');
    }

    // 商品搜索
    if (itemSearch && itemSearch.trim() !== '') {
        whereConditions.push('(item_title LIKE ? OR item_id LIKE ?)');
        const searchTerm = `%${itemSearch.trim()}%`;
        params.push(searchTerm, searchTerm);
    }

    // 直播ID筛选
    if (liveId && liveId.trim() !== '') {
        whereConditions.push('live_id = ?');
        params.push(liveId.trim());
    }

    // 金额范围筛选
    if (minAmount && !isNaN(parseFloat(minAmount))) {
        whereConditions.push('pay_amount >= ?');
        params.push(parseFloat(minAmount));
    }

    if (maxAmount && !isNaN(parseFloat(maxAmount))) {
        whereConditions.push('pay_amount <= ?');
        params.push(parseFloat(maxAmount));
    }

    const whereClause = whereConditions.length > 0 ? 'WHERE ' + whereConditions.join(' AND ') : '';
    
    return { whereClause, params };
}

/**
 * 获取订单列表
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
export async function getOrders(req, res) {
    try {
        const {
            page = 1,
            limit = 10,
            sortField = 'pay_time',
            sortOrder = 'DESC',
            ...filters
        } = req.query;

        const offset = (page - 1) * limit;
        
        // 使用公共函数构建WHERE条件
        const { whereClause, params } = buildWhereConditions(filters);

        // 验证排序字段
        const allowedSortFields = ['child_order_id', 'pay_time', 'pay_amount', 'refund_amount', 'created_at'];
        const actualSortField = allowedSortFields.includes(sortField) ? sortField : 'pay_time';
        const actualSortOrder = sortOrder.toUpperCase() === 'ASC' ? 'ASC' : 'DESC';

        // 查询订单数据
        const ordersQuery = `
            SELECT *
            FROM live_orders
            ${whereClause}
            ORDER BY ${actualSortField} ${actualSortOrder}
            LIMIT ? OFFSET ?
        `;

        const ordersParams = [...params, parseInt(limit), offset];
        const ordersResult = await database.all(ordersQuery, ordersParams);

        // 查询总数
        const countQuery = `
            SELECT COUNT(*) as total
            FROM live_orders
            ${whereClause}
        `;

        const countResult = await database.get(countQuery, params);
        const total = countResult.total;

        // 计算分页信息
        const totalPages = Math.ceil(total / limit);

        res.json({
            success: true,
            orders: ordersResult.results || [],
            pagination: {
                page: parseInt(page),
                limit: parseInt(limit),
                total: total,
                totalPages: totalPages
            }
        });

    } catch (error) {
        console.error('获取订单列表失败:', error);
        res.status(500).json({
            success: false,
            error: '获取订单列表失败: ' + error.message
        });
    }
}

/**
 * 获取订单统计信息
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
export async function getOrdersStats(req, res) {
    try {
        // 使用公共函数构建WHERE条件
        const { whereClause, params } = buildWhereConditions(req.query);

        // 统计查询
        const statsQuery = `
            SELECT 
                COUNT(*) as totalOrders,
                COALESCE(SUM(pay_amount), 0) as totalSales,
                COALESCE(SUM(refund_amount), 0) as totalRefunds,
                COALESCE(AVG(pay_amount), 0) as avgOrderValue
            FROM live_orders
            ${whereClause}
        `;

        const stats = await database.get(statsQuery, params);

        res.json({
            success: true,
            stats: {
                totalOrders: stats.totalOrders || 0,
                totalSales: stats.totalSales || 0,
                totalRefunds: stats.totalRefunds || 0,
                avgOrderValue: stats.avgOrderValue || 0
            }
        });

    } catch (error) {
        console.error('获取订单统计失败:', error);
        res.status(500).json({
            success: false,
            error: '获取订单统计失败: ' + error.message
        });
    }
}

/**
 * 同步订单数据 - 复用index.js中的handleOrderExtract逻辑
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
export async function syncOrders(req, res) {
    // 动态导入并调用已有的handleOrderExtract函数
    const { handleOrderExtract } = await import('../index.js');
    return handleOrderExtract(req, res);
}

/**
 * 导出订单数据
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
export async function exportOrders(req, res) {
    try {
        // 使用公共函数构建WHERE条件
        const { whereClause, params } = buildWhereConditions(req.query);

        // 查询所有符合条件的订单数据
        const ordersQuery = `
            SELECT 
                child_order_id as '订单号',
                anchor_name as '主播名称',
                live_id as '直播ID',
                item_title as '商品标题',
                item_id as '商品ID',
                pay_time as '支付时间',
                pay_amount as '支付金额',
                refund_amount as '退款金额',
                quantity as '数量',
                (pay_amount - refund_amount) as '净金额',
                parent_order_id as '父订单号',
                created_at as '创建时间'
            FROM live_orders
            ${whereClause}
            ORDER BY pay_time DESC
        `;

        const ordersResult = await database.all(ordersQuery, params);
        const orders = ordersResult.results || [];

        res.json({
            success: true,
            data: orders,
            total: orders.length
        });

    } catch (error) {
        console.error('导出订单数据失败:', error);
        res.status(500).json({
            success: false,
            error: '导出订单数据失败: ' + error.message
        });
    }
} 